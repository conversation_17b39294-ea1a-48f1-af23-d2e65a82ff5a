print("🧪 Quick Strategy Test")

try:
    from strategy_engine import StrategyEngine
    print("✅ Strategy engine imported")
    
    import pandas as pd
    import numpy as np
    
    # Create simple data
    data = []
    for i in range(30):
        data.append({
            'open': 1.0850 + i*0.0001,
            'high': 1.0851 + i*0.0001,
            'low': 1.0849 + i*0.0001,
            'close': 1.0850 + i*0.0001,
            'volume': 1000
        })
    
    df = pd.DataFrame(data)
    print("✅ Test data created")
    
    # Add technical indicators
    from utils import add_technical_indicators
    df = add_technical_indicators(df)
    print("✅ Technical indicators added")
    
    # Test strategies
    engine = StrategyEngine()
    
    for i in range(1, 6):  # Test first 5 strategies
        method = getattr(engine, f'evaluate_strategy_{i}')
        signal, conf = method(df)
        print(f"S{i}: signal={signal}, conf={conf:.2f}")
        
    print("🎉 Test completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
