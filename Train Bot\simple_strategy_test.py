#!/usr/bin/env python3
"""
Simple test to verify strategy fixes
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine

def create_simple_test_data():
    """Create more realistic test data that can trigger strategies"""
    np.random.seed(42)  # For consistent results
    data = []
    base_price = 1.08500

    for i in range(60):
        # Create more realistic price movements
        if i < 20:
            # Uptrend phase
            trend = 0.00005
        elif i < 40:
            # Downtrend phase
            trend = -0.00005
        else:
            # Sideways phase
            trend = np.random.randn() * 0.00001

        volatility = np.random.randn() * 0.0003
        open_price = base_price + trend + volatility

        # Create more realistic candle patterns
        if np.random.random() > 0.7:  # 30% chance of strong movement
            close_price = open_price + np.random.randn() * 0.0008
        else:
            close_price = open_price + np.random.randn() * 0.0002

        high_price = max(open_price, close_price) + abs(np.random.randn() * 0.0003)
        low_price = min(open_price, close_price) - abs(np.random.randn() * 0.0003)

        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000 + np.random.randint(0, 800)  # More volume variation
        })

        base_price = close_price

    return pd.DataFrame(data)

def test_strategies():
    """Test all strategies"""
    print("🧪 Testing Strategy Fixes...")

    # Create test data
    df = create_simple_test_data()

    # Add technical indicators (this was missing!)
    try:
        from utils import add_technical_indicators
        df = add_technical_indicators(df)
        print("✅ Technical indicators added")
    except Exception as e:
        print(f"⚠️ Could not add technical indicators: {e}")

    # Initialize strategy engine
    engine = StrategyEngine()
    
    # Test each strategy
    results = {}
    
    for i in range(1, 11):
        strategy_name = f"S{i}"
        method_name = f"evaluate_strategy_{i}"
        
        if hasattr(engine, method_name):
            method = getattr(engine, method_name)
            
            call_count = 0
            put_count = 0
            
            # Test 20 times with different data slices
            for j in range(20):
                try:
                    # Use different slices of data
                    start_idx = max(0, j)
                    test_df = df.iloc[start_idx:] if len(df) > start_idx + 30 else df

                    signal, confidence = method(test_df)
                    if signal == 1:
                        call_count += 1
                    elif signal == -1:
                        put_count += 1
                except Exception as e:
                    # Print errors for debugging
                    if j == 0:  # Only print first error to avoid spam
                        print(f"  Debug {strategy_name}: {str(e)[:50]}...")
                    pass
            
            results[strategy_name] = {'calls': call_count, 'puts': put_count}
            
            status = "✅" if put_count > 0 else "❌"
            print(f"{status} {strategy_name}: CALL={call_count}, PUT={put_count}")
    
    # Summary
    print("\n📊 Summary:")
    strategies_with_puts = sum(1 for r in results.values() if r['puts'] > 0)
    print(f"Strategies with PUT signals: {strategies_with_puts}/10")
    
    s2_working = results.get('S2', {'calls': 0, 'puts': 0})
    s2_total = s2_working['calls'] + s2_working['puts']
    print(f"Strategy 2 providing signals: {'Yes' if s2_total > 0 else 'No'}")
    
    if strategies_with_puts >= 8:
        print("🎉 SUCCESS: Most strategies now generate PUT signals!")
    else:
        print("⚠️  Some strategies still need work")

if __name__ == "__main__":
    test_strategies()
