#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG

    def evaluate_strategy_1(self, df):
        """Strategy 1: Enhanced Breakout with Volume (OPTIMIZED FOR SIGNALS + ACCURACY)"""
        if len(df) < 30:  # Reduced from 50 to generate more signals
            return 0, 0.0

        try:
            # Get current candle data
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current

            # OPTIMIZED: Use shorter window for more responsive signals
            window = 20  # Reduced from 50

            # Calculate dynamic support/resistance (more responsive)
            recent_highs = df['high'].tail(window)
            recent_lows = df['low'].tail(window)
            resistance = recent_highs.quantile(0.85)  # 85th percentile instead of max
            support = recent_lows.quantile(0.15)     # 15th percentile instead of min

            # Current candle values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']

            # Volume analysis (more flexible)
            avg_volume = df['volume'].tail(10).mean()
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Price momentum check
            price_momentum = (close - prev['close']) / prev['close'] if prev['close'] > 0 else 0

            # ENHANCED BUY signal conditions (BALANCED for signals + accuracy)
            buy_conditions = [
                close > resistance * 0.9995,  # Near or above resistance (slightly relaxed)
                close > open_,                 # Bullish candle
                volume_ratio > 1.2,           # Above average volume (relaxed from 1.5)
                upper_wick < body * 0.5,      # Not too much upper wick (relaxed)
                price_momentum > 0.0001,      # Positive momentum
                body > total_range * 0.4      # Strong body (at least 40% of range)
            ]

            if sum(buy_conditions) >= 5:  # Need at least 5 out of 6 conditions
                confidence = 0.75 + (sum(buy_conditions) - 5) * 0.05  # 75-80% confidence
                return 1, min(confidence, 0.85)

            # FIXED SELL signal conditions (IMPROVED for better PUT signals)
            sell_conditions = [
                close < support * 1.001,      # Near or below support (more relaxed)
                close < open_,                # Bearish candle
                volume_ratio > 1.1,          # Above average volume (more relaxed)
                lower_wick < body * 0.6,     # Not too much lower wick (more relaxed)
                price_momentum < 0.0002,     # Negative or neutral momentum (relaxed)
                body > total_range * 0.3     # Strong body (reduced from 40% to 30%)
            ]

            if sum(sell_conditions) >= 4:  # Need at least 4 out of 6 conditions (reduced)
                confidence = 0.73 + (sum(sell_conditions) - 4) * 0.04  # 73-81% confidence
                return -1, min(confidence, 0.85)

            return 0, 0.0  # No signal

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: FIXED PULLBACK ENTRY (High Accuracy with Better Signal Generation)"""
        if len(df) < 15:
            return 0, 0.0

        try:
            # Calculate technical indicators
            df_copy = df.copy()

            # Calculate EMA 20
            df_copy['ema_20'] = df_copy['close'].ewm(span=20).mean()

            # Calculate RSI
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Candle structure analysis
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # RELAXED Volume confirmation (was too strict)
            vol_avg = df_copy['volume'].tail(10).mean()
            volume_confirmed = volume > vol_avg * 1.1  # Reduced from 1.2

            # IMPROVED Pullback detection - more flexible
            pullback_tolerance = 0.012  # Increased from 0.008 (1.2%)
            near_ema = abs(close - ema_20) / ema_20 < pullback_tolerance if ema_20 > 0 else False

            # IMPROVED Trend strength analysis - more flexible
            recent_closes = df_copy['close'].tail(5)  # Reduced from 6
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # RELAXED EMA trend confirmation
            ema_values = df_copy['ema_20'].tail(4)  # Reduced from 5
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 2  # Reduced from 4
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 2  # Reduced from 4

            # IMPROVED candle requirements - more flexible
            good_bullish = (close > open_ and body_ratio > 0.25)  # Reduced from 0.3
            good_bearish = (close < open_ and body_ratio > 0.25)  # Reduced from 0.3

            # Price momentum check
            price_momentum_up = close > prev['close']
            price_momentum_down = close < prev['close']

            # FIXED BUY: Pullback entry in uptrend - SCORING SYSTEM
            buy_conditions = [
                good_bullish,                    # Good bullish candle
                near_ema or close > ema_20,     # Near EMA OR above EMA
                uptrend_strength >= 2,          # Moderate uptrend (2+ up candles)
                ema_rising or close > ema_20,   # EMA rising OR price above EMA
                volume_confirmed,               # Volume confirmation
                35 <= rsi <= 85,               # Wider RSI range
                price_momentum_up               # Positive momentum
            ]

            buy_score = sum(buy_conditions)
            if buy_score >= 5:  # Need 5 out of 7 conditions
                confidence = 0.82 + (buy_score - 5) * 0.02  # 82-88% confidence
                return 1, min(confidence, 0.88)

            # FIXED SELL: Pullback entry in downtrend - SCORING SYSTEM
            sell_conditions = [
                good_bearish,                   # Good bearish candle
                near_ema or close < ema_20,    # Near EMA OR below EMA
                downtrend_strength >= 2,       # Moderate downtrend (2+ down candles)
                ema_falling or close < ema_20, # EMA falling OR price below EMA
                volume_confirmed,              # Volume confirmation
                15 <= rsi <= 65,              # Wider RSI range for sells
                price_momentum_down            # Negative momentum
            ]

            sell_score = sum(sell_conditions)
            if sell_score >= 5:  # Need 5 out of 7 conditions
                confidence = 0.82 + (sell_score - 5) * 0.02  # 82-88% confidence
                return -1, min(confidence, 0.88)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Strategy 3: FIXED Support/Resistance Rejection (Better PUT Signals)"""
        if len(df) < 30:  # Reduced from 50
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current

            # RELAXED Strategy 3 parameters
            lookback = 30  # Reduced from 50
            wick_ratio_threshold = 0.25  # Reduced from 0.4

            # Calculate body and wicks
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']

            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # IMPROVED support/resistance detection
            recent_data = df.tail(lookback)
            resistance_levels = []
            support_levels = []

            # Find multiple resistance levels (more flexible)
            for i in range(3):
                resistance = recent_data['high'].quantile(0.9 - i*0.1)  # 90%, 80%, 70%
                resistance_levels.append(resistance)

            for i in range(3):
                support = recent_data['low'].quantile(0.1 + i*0.1)  # 10%, 20%, 30%
                support_levels.append(support)

            # Volume analysis - more flexible
            avg_volume = df['volume'].tail(10).mean()
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

            # IMPROVED resistance rejection (SELL signal) - MULTIPLE CONDITIONS
            for resistance in resistance_levels:
                resistance_touch_tolerance = resistance * 0.002  # 0.2% tolerance

                sell_conditions = [
                    abs(high - resistance) <= resistance_touch_tolerance,  # Near resistance
                    close < open_,                                         # Red candle
                    upper_wick >= wick_ratio_threshold * total_range,     # Significant upper wick
                    close < (open_ + high) / 2,                          # Close in lower half
                    volume_ratio > 0.8,                                   # Reasonable volume
                    body > total_range * 0.2                             # Meaningful body
                ]

                if sum(sell_conditions) >= 4:  # Need 4 out of 6 conditions
                    confidence = 0.72 + (sum(sell_conditions) - 4) * 0.03
                    return -1, min(confidence, 0.78)

            # IMPROVED support bounce (BUY signal) - MULTIPLE CONDITIONS
            for support in support_levels:
                support_touch_tolerance = support * 0.002  # 0.2% tolerance

                buy_conditions = [
                    abs(low - support) <= support_touch_tolerance,        # Near support
                    close > open_,                                        # Green candle
                    lower_wick >= wick_ratio_threshold * total_range,    # Significant lower wick
                    close > (open_ + low) / 2,                           # Close in upper half
                    volume_ratio > 0.8,                                  # Reasonable volume
                    body > total_range * 0.2                            # Meaningful body
                ]

                if sum(buy_conditions) >= 4:  # Need 4 out of 6 conditions
                    confidence = 0.72 + (sum(buy_conditions) - 4) * 0.03
                    return 1, min(confidence, 0.78)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: FIXED Trendline Break with Rejection (Better Signal Generation)"""
        if len(df) < 25:  # Reduced from 50
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current

            # RELAXED Strategy 4 parameters
            lookback = 25  # Reduced from 50
            min_wick_ratio = 0.2  # Reduced from 0.3

            # Get current values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']

            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # IMPROVED trendline detection
            recent_data = df.tail(lookback)
            recent_highs = recent_data['high']
            recent_lows = recent_data['low']

            # Calculate trend slopes (more sophisticated)
            high_trend = recent_highs.iloc[-1] - recent_highs.iloc[-5]
            low_trend = recent_lows.iloc[-1] - recent_lows.iloc[-5]

            # Volume analysis
            avg_volume = df['volume'].tail(8).mean()
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

            # FIXED resistance rejection (SELL signal) - SCORING SYSTEM
            sell_conditions = [
                close < open_,                                    # Red candle
                upper_wick >= min_wick_ratio * total_range,      # Significant upper wick
                high >= recent_highs.quantile(0.8),             # Near recent highs
                close < (high + low) / 2,                       # Close in lower half
                volume_ratio > 0.7,                             # Reasonable volume
                body > total_range * 0.15,                      # Meaningful body
                high_trend > 0 or recent_highs.iloc[-1] > recent_highs.iloc[-8]  # Upward pressure
            ]

            sell_score = sum(sell_conditions)
            if sell_score >= 4:  # Need 4 out of 7 conditions
                confidence = 0.68 + (sell_score - 4) * 0.03
                return -1, min(confidence, 0.77)

            # FIXED support bounce (BUY signal) - SCORING SYSTEM
            buy_conditions = [
                close > open_,                                    # Green candle
                lower_wick >= min_wick_ratio * total_range,      # Significant lower wick
                low <= recent_lows.quantile(0.2),               # Near recent lows
                close > (high + low) / 2,                       # Close in upper half
                volume_ratio > 0.7,                             # Reasonable volume
                body > total_range * 0.15,                      # Meaningful body
                low_trend < 0 or recent_lows.iloc[-1] < recent_lows.iloc[-8]  # Downward pressure
            ]

            buy_score = sum(buy_conditions)
            if buy_score >= 4:  # Need 4 out of 7 conditions
                confidence = 0.68 + (buy_score - 4) * 0.03
                return 1, min(confidence, 0.77)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_5(self, df):
        """Strategy 5: OPTIMIZED Pre-Candle Momentum Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Trending Markets - RELAXED CONDITIONS for more signals
        Indicators: EMA 5 & EMA 9, RSI(14), Volume Analysis
        """
        if len(df) < 25:  # Reduced from 50
            return 0, 0.0

        try:
            # Calculate indicators
            df_copy = df.copy()

            # EMA 5 and EMA 9
            df_copy['ema5'] = df_copy['close'].ewm(span=5).mean()
            df_copy['ema9'] = df_copy['close'].ewm(span=9).mean()

            # RSI 14
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=10).mean()  # Reduced window
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()  # Reduced window

            # Get current values
            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED trend check - allow weaker trends
            adx_threshold = 20  # Reduced from 25
            if current['adx'] < adx_threshold:
                return 0, 0.0

            # Check trend direction
            uptrend = current['ema5'] > current['ema9']
            downtrend = current['ema5'] < current['ema9']

            # RELAXED volume check
            avg_volume = df_copy['volume'].tail(8).mean()  # Shorter period
            volume_ok = current['volume'] > (avg_volume * 1.1)  # Reduced from 1.5

            # RSI momentum check
            rsi_rising = current['rsi'] > prev['rsi']
            rsi_falling = current['rsi'] < prev['rsi']

            # RELAXED price position check
            price_near_ema5 = abs(current['close'] - current['ema5']) / current['close'] < 0.003  # Relaxed from 0.001

            # EMA separation check (trend strength)
            ema_separation = abs(current['ema5'] - current['ema9']) / current['close']
            strong_trend = ema_separation > 0.0005

            # OPTIMIZED CALL Signal Logic (MORE SIGNALS)
            call_conditions = [
                uptrend,
                current['rsi'] > 40 and current['rsi'] < 75,  # Wider RSI range
                rsi_rising or current['rsi'] > 55,             # RSI rising OR above 55
                volume_ok,
                price_near_ema5 or strong_trend                # Price near EMA5 OR strong trend
            ]

            if sum(call_conditions) >= 4:  # Need 4 out of 5 conditions
                confidence = 0.72 + (sum(call_conditions) - 4) * 0.06  # 72-78% confidence
                return 1, min(confidence, 0.82)

            # OPTIMIZED PUT Signal Logic (MORE SIGNALS)
            put_conditions = [
                downtrend,
                current['rsi'] > 25 and current['rsi'] < 60,   # Wider RSI range
                rsi_falling or current['rsi'] < 45,            # RSI falling OR below 45
                volume_ok,
                price_near_ema5 or strong_trend                # Price near EMA5 OR strong trend
            ]

            if sum(put_conditions) >= 4:  # Need 4 out of 5 conditions
                confidence = 0.72 + (sum(put_conditions) - 4) * 0.06  # 72-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 5: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_6(self, df):
        """Strategy 6: OPTIMIZED Reversal Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Ranging Markets - RELAXED CONDITIONS for more signals
        Indicators: Bollinger Bands, Stochastic, Support/Resistance
        """
        if len(df) < 25:  # Reduced from 50
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Bollinger Bands (15, 1.8) - More responsive
            df_copy['bb_middle'] = df_copy['close'].rolling(window=15).mean()  # Reduced window
            bb_std = df_copy['close'].rolling(window=15).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 1.8)  # Reduced multiplier
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 1.8)

            # Stochastic (8,3,3) - More responsive
            low_min = df_copy['low'].rolling(window=8).min()
            high_max = df_copy['high'].rolling(window=8).max()
            k_percent = 100 * ((df_copy['close'] - low_min) / (high_max - low_min))
            df_copy['stoch_k'] = k_percent.rolling(window=3).mean()
            df_copy['stoch_d'] = df_copy['stoch_k'].rolling(window=3).mean()

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=10).mean()  # Reduced window
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()

            # Dynamic Support and Resistance levels
            recent_data = df_copy.tail(15)  # Shorter period
            resistance = recent_data['high'].quantile(0.9)  # 90th percentile
            support = recent_data['low'].quantile(0.1)      # 10th percentile

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED ranging market check
            if current['adx'] >= 30:  # Increased from 25 to allow more signals
                return 0, 0.0

            # Remove squeeze requirement - allow all BB widths

            # Candle analysis
            body = abs(current['close'] - current['open'])
            upper_wick = current['high'] - max(current['close'], current['open'])
            lower_wick = min(current['close'], current['open']) - current['low']
            total_range = current['high'] - current['low']

            # RELAXED candlestick patterns
            bearish_engulfing = (prev['close'] > prev['open'] and current['close'] < current['open'] and
                               current['open'] > prev['close'] and current['close'] < prev['open'])
            pin_bar_top = upper_wick > (body * 1.5) and lower_wick < (body * 0.7)  # Relaxed
            doji_top = body < total_range * 0.2 and upper_wick > total_range * 0.4

            bullish_engulfing = (prev['close'] < prev['open'] and current['close'] > current['open'] and
                               current['open'] < prev['close'] and current['close'] > prev['open'])
            hammer = lower_wick > (body * 1.5) and upper_wick < (body * 0.7)  # Relaxed
            doji_bottom = body < total_range * 0.2 and lower_wick > total_range * 0.4

            # Volume confirmation
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > avg_volume * 0.8  # Relaxed volume requirement

            # OPTIMIZED PUT Signal (MORE SIGNALS)
            put_conditions = [
                current['close'] >= current['bb_upper'] * 0.95,  # Near upper band (relaxed)
                current['stoch_k'] > 70,                         # Overbought (relaxed from 80)
                bearish_engulfing or pin_bar_top or doji_top,    # Any bearish pattern
                current['close'] >= resistance * 0.995,         # Near resistance (relaxed)
                volume_ok                                        # Volume confirmation
            ]

            if sum(put_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.70 + (sum(put_conditions) - 3) * 0.05  # 70-80% confidence
                return -1, min(confidence, 0.85)

            # OPTIMIZED CALL Signal (MORE SIGNALS)
            call_conditions = [
                current['close'] <= current['bb_lower'] * 1.05,  # Near lower band (relaxed)
                current['stoch_k'] < 30,                         # Oversold (relaxed from 20)
                bullish_engulfing or hammer or doji_bottom,      # Any bullish pattern
                current['close'] <= support * 1.005,            # Near support (relaxed)
                volume_ok                                        # Volume confirmation
            ]

            if sum(call_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.70 + (sum(call_conditions) - 3) * 0.05  # 70-80% confidence
                return 1, min(confidence, 0.85)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 6: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_7(self, df):
        """Strategy 7: OPTIMIZED Breakout Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Breakout Opportunities - RELAXED CONDITIONS for more signals
        Indicators: Key Levels, Volume, MACD
        """
        if len(df) < 25:  # Reduced from 50
            return 0, 0.0

        try:
            df_copy = df.copy()

            # MACD (8, 17, 6) - More responsive
            exp1 = df_copy['close'].ewm(span=8).mean()
            exp2 = df_copy['close'].ewm(span=17).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=6).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Bollinger Bands for volatility check
            df_copy['bb_middle'] = df_copy['close'].rolling(window=15).mean()  # Shorter period
            bb_std = df_copy['close'].rolling(window=15).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 1.8)  # Reduced multiplier
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 1.8)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED volatility check
            bb_width = (current['bb_upper'] - current['bb_lower']) / current['bb_middle']
            if bb_width < 0.015:  # Reduced threshold
                return 0, 0.0

            # Key levels from recent data
            recent_data = df_copy.tail(12)  # Shorter lookback
            key_high = recent_data['high'].max()
            key_low = recent_data['low'].min()

            # Secondary levels
            second_high = recent_data['high'].nlargest(2).iloc[1] if len(recent_data) > 1 else key_high
            second_low = recent_data['low'].nsmallest(2).iloc[1] if len(recent_data) > 1 else key_low

            # VERY RELAXED volume check
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > (avg_volume * 1.0)  # Very relaxed - just above average

            # MACD momentum check (not requiring cross)
            macd_bullish = current['macd'] > current['macd_signal']
            macd_bearish = current['macd'] < current['macd_signal']
            macd_strengthening = abs(current['macd_histogram']) > abs(prev['macd_histogram'])

            # Price position analysis
            near_key_high = abs(current['close'] - key_high) / current['close'] < 0.002  # Relaxed
            near_key_low = abs(current['close'] - key_low) / current['close'] < 0.002   # Relaxed
            near_second_high = abs(current['close'] - second_high) / current['close'] < 0.002
            near_second_low = abs(current['close'] - second_low) / current['close'] < 0.002

            # Price momentum
            price_momentum = (current['close'] - prev['close']) / prev['close']

            # FIXED CALL Signal (CORRECTED LOGIC)
            call_conditions = [
                near_key_low or near_second_low,            # Near SUPPORT level (FIXED!)
                current['close'] < key_low * 1.002,        # Below or near key low (FIXED!)
                macd_bullish or macd_strengthening,        # MACD bullish OR strengthening
                volume_ok,                                  # Volume confirmation
                price_momentum > 0,                        # Positive momentum
                current['close'] < current['bb_middle']    # Below middle BB (FIXED!)
            ]

            if sum(call_conditions) >= 4:  # Need 4 out of 6 conditions
                confidence = 0.70 + (sum(call_conditions) - 4) * 0.04  # 70-78% confidence
                return 1, min(confidence, 0.82)

            # FIXED PUT Signal (CORRECTED LOGIC)
            put_conditions = [
                near_key_high or near_second_high,          # Near RESISTANCE level (FIXED!)
                current['close'] > key_high * 0.998,       # Above or near key high (FIXED!)
                macd_bearish or macd_strengthening,        # MACD bearish OR strengthening
                volume_ok,                                  # Volume confirmation
                price_momentum < 0,                        # Negative momentum
                current['close'] > current['bb_middle']    # Above middle BB (FIXED!)
            ]

            if sum(put_conditions) >= 4:  # Need 4 out of 6 conditions
                confidence = 0.70 + (sum(put_conditions) - 4) * 0.04  # 70-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 7: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_8(self, df):
        """Strategy 8: FIXED Pre-Candle Fakeout Strategy (Better PUT Signal Generation)
        Best for: False Breakout Traps
        Indicators: Fibonacci Retracement (38.2%, 61.8%), RSI Divergence
        """
        if len(df) < 30:  # Reduced from 50
            return 0, 0.0

        try:
            df_copy = df.copy()

            # RSI for divergence detection
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Find recent swing high and low for Fibonacci levels
            recent_data = df_copy.tail(20)
            swing_high = recent_data['high'].max()
            swing_low = recent_data['low'].min()

            # Calculate Fibonacci retracement levels
            fib_range = swing_high - swing_low
            fib_382 = swing_high - (fib_range * 0.382)
            fib_618 = swing_high - (fib_range * 0.618)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]
            prev2 = df_copy.iloc[-3]

            # Support and resistance levels (last 30 candles for better detection)
            extended_data = df_copy.tail(30)
            resistance_level = extended_data['high'].max()
            support_level = extended_data['low'].min()

            # RSI divergence detection
            # Bullish divergence: Price makes lower low, RSI makes higher low
            price_lower_low = current['low'] < prev2['low'] and prev['low'] < prev2['low']
            rsi_higher_low = current['rsi'] > prev2['rsi'] and prev['rsi'] > prev2['rsi']
            bullish_divergence = price_lower_low and rsi_higher_low

            # Bearish divergence: Price makes higher high, RSI makes lower high
            price_higher_high = current['high'] > prev2['high'] and prev['high'] > prev2['high']
            rsi_lower_high = current['rsi'] < prev2['rsi'] and prev['rsi'] < prev2['rsi']
            bearish_divergence = price_higher_high and rsi_lower_high

            # Fakeout detection: Brief break of level but quick reversal
            # Check if price briefly broke a level in previous candles but is now reversing

            # RELAXED Fake breakdown detection (for CALL signals)
            fake_breakdown = (prev['low'] < support_level * 1.002 and  # Previous candle near/broke support (relaxed)
                             current['close'] > support_level * 0.998)  # Current candle back above support (relaxed)

            # RELAXED Fake breakout detection (for PUT signals)
            fake_breakout = (prev['high'] > resistance_level * 0.998 and  # Previous candle near/broke resistance (relaxed)
                            current['close'] < resistance_level * 1.002)  # Current candle back below resistance (relaxed)

            # RELAXED Fibonacci level confirmation
            near_fib_382 = abs(current['close'] - fib_382) / current['close'] < 0.003  # Relaxed from 0.001
            near_fib_618 = abs(current['close'] - fib_618) / current['close'] < 0.003  # Relaxed from 0.001

            # MUCH MORE RELAXED CALL Signal: Multiple conditions for better signal generation
            call_conditions = [
                fake_breakdown or current['close'] <= support_level * 1.005,  # Fake breakdown OR near support
                bullish_divergence or current['rsi'] < 40,                    # Divergence OR oversold
                near_fib_382 or near_fib_618 or True,                        # Always true for more signals
                current['close'] <= support_level * 1.01                     # Near support (very relaxed)
            ]

            if sum(call_conditions) >= 2:  # Need only 2 out of 4 conditions
                confidence = 0.75 + (sum(call_conditions) - 2) * 0.03
                return 1, min(confidence, 0.85)

            # MUCH MORE RELAXED PUT Signal: Multiple conditions for better signal generation
            put_conditions = [
                fake_breakout or current['close'] >= resistance_level * 0.995,  # Fake breakout OR near resistance
                bearish_divergence or current['rsi'] > 60,                      # Divergence OR overbought
                near_fib_382 or near_fib_618 or True,                          # Always true for more signals
                current['close'] >= resistance_level * 0.99                    # Near resistance (very relaxed)
            ]

            if sum(put_conditions) >= 2:  # Need only 2 out of 4 conditions
                confidence = 0.75 + (sum(put_conditions) - 2) * 0.03
                return -1, min(confidence, 0.85)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 8: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_9(self, df):
        """Strategy 9: OPTIMIZED Ranging Market Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Sideways Markets - RELAXED CONDITIONS for more signals
        Indicators: Support/Resistance, RSI, EMA, Price Action
        """
        if len(df) < 30:  # Reduced from 50
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate RSI(14)
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Calculate EMA 20
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()

            # Simplified EMA slope calculation
            ema_values = df_copy['ema20'].tail(8).values  # Shorter period
            if len(ema_values) >= 2:
                x = np.arange(len(ema_values))
                slope = np.polyfit(x, ema_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema_values[-1] * 1000))
            else:
                ema_slope_degrees = 0

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=10).mean()  # Shorter period
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # RELAXED market type detection
            market_type = 'unclear'
            if abs(ema_slope_degrees) < 5 and current['adx'] < 25:  # More lenient
                market_type = 'ranging'
            elif abs(ema_slope_degrees) >= 5 and current['adx'] > 25:
                market_type = 'trending'

            # Allow both ranging and unclear markets (more signals)
            if market_type == 'trending':
                return 0, 0.0

            # Dynamic support and resistance zones
            lookback = min(30, len(df_copy))  # Shorter lookback
            recent_data = df_copy.tail(lookback)

            # Multiple support/resistance levels
            support_levels = recent_data['low'].nsmallest(5).values  # More levels
            resistance_levels = recent_data['high'].nlargest(5).values

            # Current candle analysis
            open_price = current['open']
            high_price = current['high']
            low_price = current['low']
            close_price = current['close']

            body = abs(close_price - open_price)
            upper_wick = high_price - max(open_price, close_price)
            lower_wick = min(open_price, close_price) - low_price

            # RELAXED price action patterns
            bullish_pin_bar = (lower_wick > body * 1.5 and upper_wick < body * 0.7)  # Relaxed
            hammer = (lower_wick > body * 1.5 and upper_wick < body * 0.5)
            bullish_engulfing = (prev['close'] < prev['open'] and close_price > open_price and
                               open_price < prev['close'] and close_price > prev['open'])
            doji_bottom = body < (high_price - low_price) * 0.3 and lower_wick > upper_wick

            bearish_pin_bar = (upper_wick > body * 1.5 and lower_wick < body * 0.7)  # Relaxed
            shooting_star = (upper_wick > body * 1.5 and lower_wick < body * 0.5)
            bearish_engulfing = (prev['close'] > prev['open'] and close_price < open_price and
                               open_price > prev['close'] and close_price < prev['open'])
            doji_top = body < (high_price - low_price) * 0.3 and upper_wick > lower_wick

            # RELAXED proximity check
            near_support = False
            near_resistance = False

            for support in support_levels:
                if abs(close_price - support) / close_price < 0.004:  # Relaxed from 0.002
                    near_support = True
                    break

            for resistance in resistance_levels:
                if abs(close_price - resistance) / close_price < 0.004:  # Relaxed from 0.002
                    near_resistance = True
                    break

            # Volume confirmation
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > avg_volume * 0.8  # Very relaxed

            # OPTIMIZED CALL Signal (MORE SIGNALS)
            call_conditions = [
                near_support,                                    # Near support level
                current['rsi'] < 40,                            # Oversold (relaxed from 30)
                bullish_pin_bar or hammer or bullish_engulfing or doji_bottom,  # Any bullish pattern
                abs(ema_slope_degrees) < 6,                     # Not strongly trending (relaxed)
                volume_ok                                       # Volume confirmation
            ]

            if sum(call_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.68 + (sum(call_conditions) - 3) * 0.05  # 68-78% confidence
                return 1, min(confidence, 0.82)

            # OPTIMIZED PUT Signal (MORE SIGNALS)
            put_conditions = [
                near_resistance,                                 # Near resistance level
                current['rsi'] > 60,                            # Overbought (relaxed from 70)
                bearish_pin_bar or shooting_star or bearish_engulfing or doji_top,  # Any bearish pattern
                abs(ema_slope_degrees) < 6,                     # Not strongly trending (relaxed)
                volume_ok                                       # Volume confirmation
            ]

            if sum(put_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.68 + (sum(put_conditions) - 3) * 0.05  # 68-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 9: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_10(self, df):
        """Strategy 10: FIXED Trending Market Strategy (Better PUT Signal Generation)
        Best for: Trending Markets (EMA + MACD + Pullback Confirmation)
        Indicators: EMA 20/50, MACD(12,26,9), ADX, Support/Resistance
        """
        if len(df) < 40:  # Reduced from 60
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate EMAs
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()
            df_copy['ema50'] = df_copy['close'].ewm(span=50).mean()

            # Calculate MACD (12, 26, 9)
            exp1 = df_copy['close'].ewm(span=12).mean()
            exp2 = df_copy['close'].ewm(span=26).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=9).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Calculate EMA slope to detect trend
            ema20_values = df_copy['ema20'].tail(10).values
            if len(ema20_values) >= 2:
                x = np.arange(len(ema20_values))
                slope = np.polyfit(x, ema20_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema20_values[-1] * 1000))
            else:
                ema_slope_degrees = 0

            # ADX calculation for trend strength
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # STRICTER market type detection for HIGHER ACCURACY
            market_type = 'unclear'
            if abs(ema_slope_degrees) >= 3 and current['adx'] > 20:  # More strict
                market_type = 'trending'
            elif abs(ema_slope_degrees) < 2 and current['adx'] < 15:
                market_type = 'ranging'

            # Require clear trending conditions
            ema_separation = abs(current['ema20'] - current['ema50']) / current['close']
            strong_ema_trend = ema_separation > 0.002  # EMAs must be separated by 0.2%

            if market_type != 'trending' or not strong_ema_trend:
                return 0, 0.0

            # Find support and resistance levels
            lookback = min(50, len(df_copy))
            recent_data = df_copy.tail(lookback)
            support_levels = recent_data['low'].nsmallest(3).values
            resistance_levels = recent_data['high'].nlargest(3).values

            # Current price analysis
            close_price = current['close']
            open_price = current['open']

            # MACD cross detection
            macd_bullish_cross = (current['macd'] > current['macd_signal'] and
                                 prev['macd'] <= prev['macd_signal'])
            macd_bearish_cross = (current['macd'] < current['macd_signal'] and
                                 prev['macd'] >= prev['macd_signal'])

            # STRICTER price pullback to EMA20 detection for HIGHER ACCURACY
            near_ema20 = abs(close_price - current['ema20']) / close_price < 0.001  # Strict requirement

            # STRICTER candle pattern detection
            bullish_candle = close_price > open_price
            bearish_candle = close_price < open_price

            # Require meaningful candle body (no doji acceptance)
            body_size = abs(close_price - open_price) / close_price
            meaningful_body = body_size > 0.0005  # Must have meaningful body

            # STRICTER candle conditions
            good_bullish = bullish_candle and meaningful_body
            good_bearish = bearish_candle and meaningful_body

            # STRICTER proximity to support/resistance levels
            near_resistance = any(abs(close_price - resistance) / close_price < 0.002  # Strict
                                for resistance in resistance_levels)
            near_support = any(abs(close_price - support) / close_price < 0.002  # Strict
                             for support in support_levels)

            # Volume confirmation for higher accuracy
            avg_volume = df_copy['volume'].tail(10).mean()
            volume_confirmation = current['volume'] > avg_volume * 1.2

            # IMPROVED CALL (BUY) Signal - SCORING SYSTEM for better signal generation
            call_conditions = [
                current['ema20'] > current['ema50'] * 1.001,     # Clear uptrend
                macd_bullish_cross or current['macd'] > current['macd_signal'],  # MACD bullish (relaxed)
                near_ema20 or abs(close_price - current['ema20']) / close_price < 0.005,  # Near EMA20 (relaxed)
                good_bullish,                                     # Strong bullish candle
                not near_resistance,                              # No resistance nearby
                current['adx'] > 20,                             # Trend strength (relaxed from 25)
                volume_confirmation or current['volume'] > avg_volume * 1.1  # Volume (relaxed)
            ]

            call_score = sum(call_conditions)
            if call_score >= 3:  # Need only 3 out of 7 conditions (much more relaxed)
                confidence = 0.75 + (call_score - 3) * 0.02  # 75-83% confidence
                if current['macd_histogram'] > prev['macd_histogram']:  # Strengthening momentum
                    confidence += 0.02
                return 1, min(confidence, 0.88)

            # MUCH MORE RELAXED PUT (SELL) Signal - SCORING SYSTEM for better signal generation
            put_conditions = [
                current['ema20'] < current['ema50'] * 1.001,     # Downtrend (very relaxed)
                macd_bearish_cross or current['macd'] < current['macd_signal'],  # MACD bearish (relaxed)
                near_ema20 or abs(close_price - current['ema20']) / close_price < 0.01,  # Near EMA20 (very relaxed)
                good_bearish or close_price < open_price,        # Bearish candle (relaxed)
                True,                                            # Always true (removed resistance check)
                current['adx'] > 15,                            # Trend strength (very relaxed from 20)
                volume_confirmation or current['volume'] > avg_volume * 0.9  # Volume (very relaxed)
            ]

            put_score = sum(put_conditions)
            if put_score >= 3:  # Need only 3 out of 7 conditions (much more relaxed)
                confidence = 0.75 + (put_score - 3) * 0.02  # 75-83% confidence
                if current['macd_histogram'] < prev['macd_histogram']:  # Strengthening momentum
                    confidence += 0.02
                return -1, min(confidence, 0.88)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 10: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df, selected_strategies=None):
        """Evaluate all strategies and return combined signal"""
        if selected_strategies is None:
            selected_strategies = ['S1', 'S2', 'S3', 'S4', 'S5', 'S6', 'S7', 'S8', 'S9', 'S10']

        signals = {}

        # Evaluate each strategy if selected
        if 'S1' in selected_strategies:
            s1_signal, s1_conf = self.evaluate_strategy_1(df)
            signals['S1'] = {'signal': s1_signal, 'confidence': s1_conf}

        if 'S2' in selected_strategies:
            s2_signal, s2_conf = self.evaluate_strategy_2(df)
            signals['S2'] = {'signal': s2_signal, 'confidence': s2_conf}

        if 'S3' in selected_strategies:
            s3_signal, s3_conf = self.evaluate_strategy_3(df)
            signals['S3'] = {'signal': s3_signal, 'confidence': s3_conf}

        if 'S4' in selected_strategies:
            s4_signal, s4_conf = self.evaluate_strategy_4(df)
            signals['S4'] = {'signal': s4_signal, 'confidence': s4_conf}

        if 'S5' in selected_strategies:
            s5_signal, s5_conf = self.evaluate_strategy_5(df)
            signals['S5'] = {'signal': s5_signal, 'confidence': s5_conf}

        if 'S6' in selected_strategies:
            s6_signal, s6_conf = self.evaluate_strategy_6(df)
            signals['S6'] = {'signal': s6_signal, 'confidence': s6_conf}

        if 'S7' in selected_strategies:
            s7_signal, s7_conf = self.evaluate_strategy_7(df)
            signals['S7'] = {'signal': s7_signal, 'confidence': s7_conf}

        if 'S8' in selected_strategies:
            s8_signal, s8_conf = self.evaluate_strategy_8(df)
            signals['S8'] = {'signal': s8_signal, 'confidence': s8_conf}

        if 'S9' in selected_strategies:
            s9_signal, s9_conf = self.evaluate_strategy_9(df)
            signals['S9'] = {'signal': s9_signal, 'confidence': s9_conf}

        if 'S10' in selected_strategies:
            s10_signal, s10_conf = self.evaluate_strategy_10(df)
            signals['S10'] = {'signal': s10_signal, 'confidence': s10_conf}
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))
