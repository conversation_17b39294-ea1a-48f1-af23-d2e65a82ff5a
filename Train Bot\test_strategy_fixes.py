#!/usr/bin/env python3
"""
Test script to verify all strategy fixes for PUT signals and Strategy 2
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_engine import StrategyEngine
from utils import print_colored, print_header

def create_test_data():
    """Create realistic test data for strategy testing"""
    np.random.seed(42)  # For reproducible results
    
    # Create 100 candles of test data
    data = []
    base_price = 1.08500
    
    for i in range(100):
        # Add some trend and volatility
        trend = 0.00001 * (i - 50)  # Slight trend
        volatility = np.random.randn() * 0.0005
        
        open_price = base_price + trend + volatility
        close_price = open_price + np.random.randn() * 0.0003
        high_price = max(open_price, close_price) + abs(np.random.randn() * 0.0002)
        low_price = min(open_price, close_price) - abs(np.random.randn() * 0.0002)
        
        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000 + np.random.randint(0, 500)
        })
        
        base_price = close_price
    
    return pd.DataFrame(data)

def test_all_strategies():
    """Test all 10 strategies for both CALL and PUT signals"""
    print_header("🧪 TESTING ALL STRATEGY FIXES")
    
    # Create test data
    df = create_test_data()
    
    # Add technical indicators
    from utils import add_technical_indicators
    df = add_technical_indicators(df)
    
    # Initialize strategy engine
    engine = StrategyEngine()
    
    # Test results
    results = {
        'strategy': [],
        'call_signals': [],
        'put_signals': [],
        'hold_signals': [],
        'total_tests': [],
        'put_percentage': []
    }
    
    # Test each strategy multiple times with different data
    for strategy_num in range(1, 11):
        strategy_name = f"S{strategy_num}"
        method_name = f"evaluate_strategy_{strategy_num}"
        
        if hasattr(engine, method_name):
            method = getattr(engine, method_name)
            
            call_count = 0
            put_count = 0
            hold_count = 0
            total_tests = 50  # Test 50 times with different data slices
            
            print_colored(f"Testing {strategy_name}...", "INFO")
            
            for test_run in range(total_tests):
                # Use different slices of data for each test
                start_idx = max(0, test_run)
                test_df = df.iloc[start_idx:start_idx+50] if len(df) > start_idx+50 else df.iloc[start_idx:]
                
                if len(test_df) < 20:
                    continue
                    
                try:
                    signal, confidence = method(test_df)
                    
                    if signal == 1:
                        call_count += 1
                    elif signal == -1:
                        put_count += 1
                    else:
                        hold_count += 1
                        
                except Exception as e:
                    print_colored(f"  ❌ Error in {strategy_name}: {str(e)}", "ERROR")
                    hold_count += 1
            
            # Calculate percentages
            put_percentage = (put_count / total_tests) * 100 if total_tests > 0 else 0
            
            # Store results
            results['strategy'].append(strategy_name)
            results['call_signals'].append(call_count)
            results['put_signals'].append(put_count)
            results['hold_signals'].append(hold_count)
            results['total_tests'].append(total_tests)
            results['put_percentage'].append(put_percentage)
            
            # Display results
            status = "✅" if put_count > 0 else "❌"
            print_colored(f"  {status} {strategy_name}: CALL={call_count}, PUT={put_count}, HOLD={hold_count} (PUT: {put_percentage:.1f}%)", 
                         "SUCCESS" if put_count > 0 else "ERROR")
    
    return results

def analyze_results(results):
    """Analyze and display test results"""
    print_header("📊 STRATEGY FIX ANALYSIS")
    
    total_strategies = len(results['strategy'])
    strategies_with_puts = sum(1 for puts in results['put_signals'] if puts > 0)
    
    print_colored(f"Total Strategies Tested: {total_strategies}", "INFO")
    print_colored(f"Strategies with PUT signals: {strategies_with_puts}/{total_strategies}", 
                 "SUCCESS" if strategies_with_puts == total_strategies else "WARNING")
    
    print()
    print_colored("Strategy Performance Summary:", "OCEAN", bold=True)
    
    for i, strategy in enumerate(results['strategy']):
        put_count = results['put_signals'][i]
        put_percentage = results['put_percentage'][i]
        
        if put_count == 0:
            status = "❌ NO PUT SIGNALS"
            color = "ERROR"
        elif put_percentage < 10:
            status = f"⚠️  LOW PUT SIGNALS ({put_percentage:.1f}%)"
            color = "WARNING"
        elif put_percentage < 30:
            status = f"✅ MODERATE PUT SIGNALS ({put_percentage:.1f}%)"
            color = "SUCCESS"
        else:
            status = f"🔥 GOOD PUT SIGNALS ({put_percentage:.1f}%)"
            color = "SUCCESS"
        
        print_colored(f"  {strategy}: {status}", color)
    
    # Special check for Strategy 2
    s2_index = results['strategy'].index('S2') if 'S2' in results['strategy'] else -1
    if s2_index >= 0:
        s2_total_signals = results['call_signals'][s2_index] + results['put_signals'][s2_index]
        s2_signal_rate = (s2_total_signals / results['total_tests'][s2_index]) * 100
        
        print()
        print_colored("Strategy 2 Special Analysis:", "OCEAN", bold=True)
        if s2_total_signals == 0:
            print_colored("  ❌ Strategy 2 still not providing any signals", "ERROR")
        elif s2_signal_rate < 20:
            print_colored(f"  ⚠️  Strategy 2 providing few signals ({s2_signal_rate:.1f}%)", "WARNING")
        else:
            print_colored(f"  ✅ Strategy 2 now providing signals ({s2_signal_rate:.1f}%)", "SUCCESS")

def main():
    """Run all tests"""
    print_header("🚀 STRATEGY FIX VERIFICATION")
    print_colored("Testing all 10 strategies for PUT signal generation and Strategy 2 fixes...", "OCEAN")
    print()
    
    # Run tests
    results = test_all_strategies()
    
    print()
    
    # Analyze results
    analyze_results(results)
    
    print()
    print_header("🎉 FIX SUMMARY")
    
    # Count successful fixes
    strategies_with_puts = sum(1 for puts in results['put_signals'] if puts > 0)
    s2_working = results['put_signals'][results['strategy'].index('S2')] + results['call_signals'][results['strategy'].index('S2')] > 0
    
    print_colored("✅ FIXES IMPLEMENTED:", "SUCCESS", bold=True)
    print_colored("  • Strategy 1: Relaxed PUT signal conditions", "SUCCESS")
    print_colored("  • Strategy 2: Fixed restrictive conditions (scoring system)", "SUCCESS")
    print_colored("  • Strategy 3: Improved support/resistance detection", "SUCCESS")
    print_colored("  • Strategy 4: Enhanced trendline break detection", "SUCCESS")
    print_colored("  • Strategy 8: Relaxed fakeout detection conditions", "SUCCESS")
    print_colored("  • Strategy 10: Implemented scoring system for signals", "SUCCESS")
    
    print()
    print_colored("📊 RESULTS:", "OCEAN", bold=True)
    print_colored(f"  • Strategies with PUT signals: {strategies_with_puts}/10", 
                 "SUCCESS" if strategies_with_puts >= 8 else "WARNING")
    print_colored(f"  • Strategy 2 working: {'Yes' if s2_working else 'No'}", 
                 "SUCCESS" if s2_working else "ERROR")
    
    if strategies_with_puts >= 8 and s2_working:
        print_colored("🎉 ALL FIXES SUCCESSFUL! Strategies now generate both CALL and PUT signals!", "SUCCESS", bold=True)
    else:
        print_colored("⚠️  Some strategies may need additional adjustments", "WARNING")

if __name__ == "__main__":
    main()
